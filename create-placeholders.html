<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casino Game Placeholder Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #FFD700;
            border-radius: 10px;
            margin: 10px;
        }
        button {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            transform: scale(1.05);
        }
        h2 {
            color: #FFD700;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>Casino Game Placeholder Image Generator</h1>
    
    <div class="canvas-container">
        <h2>Slot Online</h2>
        <canvas id="slotCanvas" width="400" height="300"></canvas>
        <br>
        <button onclick="downloadImage('slotCanvas', 'slot-online.jpg')">Download Slot Image</button>
    </div>

    <div class="canvas-container">
        <h2>Sportsbook</h2>
        <canvas id="sportsbookCanvas" width="400" height="300"></canvas>
        <br>
        <button onclick="downloadImage('sportsbookCanvas', 'sportsbook.jpg')">Download Sportsbook Image</button>
    </div>

    <div class="canvas-container">
        <h2>Live Casino</h2>
        <canvas id="casinoCanvas" width="400" height="300"></canvas>
        <br>
        <button onclick="downloadImage('casinoCanvas', 'live-casino.jpg')">Download Casino Image</button>
    </div>

    <div class="canvas-container">
        <h2>Poker Online</h2>
        <canvas id="pokerCanvas" width="400" height="300"></canvas>
        <br>
        <button onclick="downloadImage('pokerCanvas', 'poker-online.jpg')">Download Poker Image</button>
    </div>

    <script>
        // Slot Machine Placeholder
        function createSlotImage() {
            const canvas = document.getElementById('slotCanvas');
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#8B0000');
            gradient.addColorStop(1, '#DC143C');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Slot machine frame
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(50, 50, 300, 200);
            ctx.fillStyle = '#000';
            ctx.fillRect(70, 70, 260, 160);
            
            // Slot reels
            const reels = ['🍒', '🍋', '⭐', '💎', '🎰'];
            ctx.font = '60px Arial';
            ctx.textAlign = 'center';
            
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(90 + i * 80, 90, 60, 120);
                ctx.fillStyle = '#000';
                ctx.fillText(reels[i % reels.length], 120 + i * 80, 170);
            }
            
            // Title
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('SLOT ONLINE', 200, 280);
        }

        // Sportsbook Placeholder
        function createSportsbookImage() {
            const canvas = document.getElementById('sportsbookCanvas');
            const ctx = canvas.getContext('2d');
            
            // Background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#006400');
            gradient.addColorStop(1, '#228B22');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Soccer field
            ctx.fillStyle = '#32CD32';
            ctx.fillRect(50, 100, 300, 150);
            
            // Field lines
            ctx.strokeStyle = '#FFF';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(200, 100);
            ctx.lineTo(200, 250);
            ctx.arc(200, 175, 40, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Soccer ball
            ctx.fillStyle = '#FFF';
            ctx.beginPath();
            ctx.arc(200, 175, 20, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Title
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('SPORTSBOOK', 200, 50);
        }

        // Live Casino Placeholder
        function createCasinoImage() {
            const canvas = document.getElementById('casinoCanvas');
            const ctx = canvas.getContext('2d');
            
            // Background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#0D0D0D');
            gradient.addColorStop(1, '#2D2D2D');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Casino table
            ctx.fillStyle = '#006400';
            ctx.fillRect(50, 150, 300, 100);
            
            // Roulette wheel
            ctx.fillStyle = '#8B0000';
            ctx.beginPath();
            ctx.arc(150, 120, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(150, 120, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cards
            ctx.fillStyle = '#FFF';
            ctx.fillRect(250, 80, 40, 60);
            ctx.fillRect(270, 70, 40, 60);
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 80, 40, 60);
            ctx.strokeRect(270, 70, 40, 60);
            
            // Title
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('LIVE CASINO', 200, 280);
        }

        // Poker Placeholder
        function createPokerImage() {
            const canvas = document.getElementById('pokerCanvas');
            const ctx = canvas.getContext('2d');
            
            // Background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#8B0000');
            gradient.addColorStop(1, '#DC143C');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Poker table
            ctx.fillStyle = '#006400';
            ctx.beginPath();
            ctx.ellipse(200, 150, 150, 80, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Playing cards
            const cardPositions = [
                {x: 150, y: 120}, {x: 180, y: 115}, {x: 210, y: 115}, 
                {x: 240, y: 120}, {x: 270, y: 130}
            ];
            
            cardPositions.forEach((pos, i) => {
                ctx.fillStyle = '#FFF';
                ctx.fillRect(pos.x - 15, pos.y - 20, 30, 40);
                ctx.strokeStyle = '#000';
                ctx.lineWidth = 1;
                ctx.strokeRect(pos.x - 15, pos.y - 20, 30, 40);
                
                // Card symbols
                ctx.fillStyle = i % 2 === 0 ? '#DC143C' : '#000';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                const suits = ['♠', '♥', '♦', '♣'];
                ctx.fillText(suits[i % 4], pos.x, pos.y + 5);
            });
            
            // Poker chips
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = '#FFD700';
                ctx.beginPath();
                ctx.arc(100 + i * 30, 200, 15, 0, 2 * Math.PI);
                ctx.fill();
                ctx.strokeStyle = '#8B0000';
                ctx.lineWidth = 3;
                ctx.stroke();
            }
            
            // Title
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('POKER ONLINE', 200, 50);
        }

        // Download function
        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }

        // Generate all images on load
        window.onload = function() {
            createSlotImage();
            createSportsbookImage();
            createCasinoImage();
            createPokerImage();
        };
    </script>
</body>
</html>
