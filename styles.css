/* ===== CASINO THEME CSS - StarBet88 ===== */
/* CSS Reset & Base Styles */
* { margin: 0; padding: 0; box-sizing: border-box; }
:root {
  /* Casino Color Palette */
  --primary-gold: #FFD700; --deep-red: #8B0000; --casino-red: #DC143C; --rich-black: #0D0D0D;
  --dark-green: #006400; --emerald: #50C878; --silver: #C0C0C0; --white: #FFFFFF; --cream: #F5F5DC;
  /* Gradients */
  --gold-gradient: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
  --red-gradient: linear-gradient(135deg, #DC143C, #8B0000);
  --dark-gradient: linear-gradient(135deg, #0D0D0D, #1A1A1A, #2D2D2D);
  --green-gradient: linear-gradient(135deg, #006400, #228B22);
  /* Typography */
  --font-primary: 'Georgia', 'Times New Roman', serif;
  --font-secondary: 'Arial', 'Helvetica', sans-serif;
  --font-accent: 'Impact', 'Arial Black', sans-serif;
  /* Shadows & Effects */
  --gold-shadow: 0 4px 15px rgba(255, 215, 0, 0.3); --red-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
  --dark-shadow: 0 8px 25px rgba(0, 0, 0, 0.5); --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}
body {
  font-family: var(--font-secondary); line-height: 1.6; color: var(--white);
  background: var(--dark-gradient); background-attachment: fixed; overflow-x: hidden; padding-top: 80px;
}

/* Mobile body padding adjustment */
@media (max-width: 768px) { body { padding-top: 70px; } }
@media (max-width: 480px) { body { padding-top: 65px; } }
@media (max-width: 320px) { body { padding-top: 60px; } }
/* Typography Styles */
h1, h2, h3 { font-family: var(--font-primary); font-weight: bold; text-shadow: var(--text-shadow); margin-bottom: 1rem; }
h1 { font-size: clamp(2.5rem, 5vw, 4rem); color: var(--primary-gold); text-align: center; letter-spacing: 2px; }
h2 { font-size: clamp(2rem, 4vw, 3rem); color: var(--primary-gold); text-align: center; margin-bottom: 2rem; }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); color: var(--silver); }
p { font-size: 1.1rem; margin-bottom: 1rem; color: var(--cream); }

/* Header Styles */
.main-header {
  position: fixed; top: 0; left: 0; right: 0; background: var(--dark-gradient);
  border-bottom: 3px solid var(--primary-gold); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.8); z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); will-change: background, backdrop-filter, box-shadow; contain: layout style paint;
}
.header-container {
  max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between;
  padding: 0.5rem 2rem; height: 80px; contain: layout style;
}
/* Consolidated logo styles */
.logo {
  display: flex; align-items: center; text-decoration: none; cursor: pointer;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); will-change: transform;
}
.logo:hover { transform: scale(1.05); }
.logo:focus { outline: 3px solid var(--primary-gold); outline-offset: 4px; border-radius: 4px; }
.logo-img {
  height: 50px; width: auto; max-width: 180px; filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.7));
  transition: filter 0.4s cubic-bezier(0.4, 0, 0.2, 1); will-change: filter;
}
.logo-img:hover { filter: drop-shadow(2px 2px 8px rgba(255, 215, 0, 0.5)); }
.main-nav { display: flex; align-items: center; contain: layout style; }

.nav-list { display: flex; list-style: none; margin: 0; padding: 0; gap: 2rem; }
/* Enhanced navigation links with improved accessibility */
.nav-link {
  color: var(--silver); text-decoration: none; font-weight: bold; font-size: 1.1rem; text-transform: uppercase; letter-spacing: 1px;
  padding: 0.75rem 1.25rem; min-height: 44px; min-width: 44px; display: flex; align-items: center; justify-content: center;
  border-radius: 25px; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden;
  will-change: transform, color, box-shadow;
}
/* Enhanced gold gradient hover effect */
.nav-link::before {
  content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
  background: var(--gold-gradient); transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1); z-index: -1;
}
/* Improved hover states */
.nav-link:hover {
  color: var(--rich-black); transform: translateY(-3px) scale(1.02); box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}
.nav-link:hover::before { left: 0; }
/* Enhanced focus states for accessibility */
.nav-link:focus {
  outline: 3px solid var(--primary-gold); outline-offset: 3px; color: var(--rich-black); background: var(--gold-gradient);
}

.nav-link:active { transform: translateY(-1px) scale(1.01); transition-duration: 0.1s; }
/* Enhanced Mobile Menu Toggle with Accessibility */
.mobile-menu-toggle {
  display: none; flex-direction: column; justify-content: center; align-items: center; cursor: pointer;
  padding: 0.75rem; min-height: 44px; min-width: 44px; border: none; background: transparent; border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); will-change: background, transform;
}
.mobile-menu-toggle:hover { background: rgba(255, 215, 0, 0.15); transform: scale(1.05); }
/* Enhanced focus state for accessibility */
.mobile-menu-toggle:focus { outline: 3px solid var(--primary-gold); outline-offset: 2px; background: rgba(255, 215, 0, 0.1); }
/* Improved hamburger line animations */
.hamburger-line {
  width: 28px; height: 3px; background: var(--primary-gold); margin: 3px 0; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px; transform-origin: center; will-change: transform, opacity;
}
/* Enhanced active state animations */
.mobile-menu-toggle.active .hamburger-line:nth-child(1) { transform: rotate(45deg) translate(7px, 7px); }
.mobile-menu-toggle.active .hamburger-line:nth-child(2) { opacity: 0; transform: scale(0); }
.mobile-menu-toggle.active .hamburger-line:nth-child(3) { transform: rotate(-45deg) translate(7px, -7px); }
/* Enhanced header scroll effect with performance optimization */
.main-header.scrolled {
  background: rgba(13, 13, 13, 0.95); backdrop-filter: blur(10px); border-bottom-color: var(--casino-red); box-shadow: 0 6px 25px rgba(0, 0, 0, 0.9);
}

/* Progressive enhancement for backdrop-filter */
@supports (backdrop-filter: blur(10px)) { .main-header.scrolled { backdrop-filter: blur(10px); } }
/* Active nav link styling with enhanced visibility */
.nav-link.active {
  background: var(--gold-gradient); color: var(--rich-black); box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4); transform: translateY(-2px);
}
/* Smooth scroll behavior with reduced motion support */
html { scroll-behavior: smooth; }
@media (prefers-reduced-motion: reduce) {
  html { scroll-behavior: auto; }
  .main-header, .logo, .nav-link, .mobile-menu-toggle, .hamburger-line { transition-duration: 0.01ms !important; }
}

/* Hero Section */
.hero {
  min-height: 100vh; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; padding: 2rem;
  background: radial-gradient(circle at 20% 80%, rgba(220, 20, 60, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.2) 0%, transparent 50%), var(--dark-gradient);
  position: relative; overflow: hidden;
}
.hero::before {
  content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="casino-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23casino-pattern)"/></svg>');
  opacity: 0.3; z-index: -1;
}
/* Hero Carousel */
.hero-carousel {
  position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1; opacity: 0.2;
}
.carousel-container {
  position: relative; width: 100%; height: 100%; overflow: hidden;
}
.carousel-slide {
  position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0;
  transition: opacity 1s ease-in-out; display: flex; align-items: center; justify-content: center;
}
.carousel-slide.active { opacity: 1; }
.banner-img {
  max-width: 400px; height: auto; border-radius: 20px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
  filter: brightness(0.8) contrast(1.1) saturate(1.2); will-change: transform, opacity;
  transform: scale(0.9); transition: transform 0.5s ease;
}
.carousel-slide.active .banner-img { transform: scale(1); }
/* Carousel Navigation */
.carousel-nav {
  position: absolute; top: 50%; left: 0; right: 0; z-index: 3; pointer-events: none;
  display: flex; justify-content: space-between; padding: 0 2rem;
}
.carousel-arrow {
  background: rgba(255, 215, 0, 0.8); color: var(--rich-black); border: none; border-radius: 50%;
  width: 50px; height: 50px; cursor: pointer; pointer-events: auto; font-size: 1.5rem; font-weight: bold;
  display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3); will-change: transform, background;
}
.carousel-arrow:hover {
  background: var(--primary-gold); transform: scale(1.1); box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}
.carousel-arrow:focus { outline: 2px solid var(--primary-gold); outline-offset: 2px; }
/* Carousel Dots */
.carousel-dots {
  position: absolute; bottom: 2rem; left: 50%; transform: translateX(-50%); z-index: 3;
  display: flex; gap: 1rem; pointer-events: auto;
}
.carousel-dot {
  width: 12px; height: 12px; border-radius: 50%; border: 2px solid var(--primary-gold);
  background: transparent; cursor: pointer; transition: all 0.3s ease; will-change: background, transform;
}
.carousel-dot.active { background: var(--primary-gold); transform: scale(1.2); }
.carousel-dot:hover { background: rgba(255, 215, 0, 0.7); transform: scale(1.1); }
.carousel-dot:focus { outline: 2px solid var(--primary-gold); outline-offset: 2px; }
/* Hero Content */
.hero-content { position: relative; z-index: 2; }
.hero-content p { font-size: 1.3rem; max-width: 600px; margin: 2rem auto; color: var(--cream); }

/* Button Styles */
button, .cta-button {
  background: var(--gold-gradient); color: var(--cream); border: none; padding: 1rem 2.5rem; font-size: 1.2rem; font-weight: bold;
  font-family: var(--font-accent); text-transform: uppercase; letter-spacing: 1px; border-radius: 50px; cursor: pointer;
  box-shadow: var(--gold-shadow); transition: all 0.3s ease; position: relative; overflow: hidden;
}
button:hover, .cta-button:hover { transform: translateY(-3px) scale(1.05); box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5); }

button:active, .cta-button:active {
  transform: translateY(-1px) scale(1.02);
}

button::before, .cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

button:hover::before, .cta-button:hover::before {
  left: 100%;
}

/* Section Styling */
section {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* USP Section */
.usp {
  background: rgba(0, 100, 0, 0.1);
  border-radius: 20px;
  margin: 2rem auto;
  border: 2px solid var(--primary-gold);
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature {
  background: var(--red-gradient);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: var(--red-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid var(--primary-gold);
}

.feature:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 15px 35px rgba(220, 20, 60, 0.4);
}

.feature h3 {
  color: var(--primary-gold);
  margin-bottom: 1rem;
}

/* Enhanced Games Section */
.games {
  padding: 4rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.games::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(220, 20, 60, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.games-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
}

.games-header h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  background: var(--gold-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  position: relative;
}

.games-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--gold-gradient);
  border-radius: 2px;
}

.games-subtitle {
  font-size: 1.3rem;
  color: var(--silver);
  font-style: italic;
  max-width: 600px;
  margin: 0 auto;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-bottom: 4rem;
}

.game-card {
  background: var(--dark-gradient);
  border-radius: 25px;
  overflow: hidden;
  border: 3px solid var(--primary-gold);
  box-shadow: var(--dark-shadow);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  will-change: transform;
}


.game-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(from 0deg, var(--primary-gold), var(--casino-red), var(--primary-gold));
  border-radius: 25px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.game-card:hover::before {
  opacity: 0.7;
}

.game-card:hover {
  transform: translateY(-20px) scale(1.03);
  box-shadow: 0 25px 50px rgba(255, 215, 0, 0.4);
}

.game-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--casino-red);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4);
  animation: pulse 2s infinite;
}

.game-card.featured .game-badge {
  background: var(--gold-gradient);
  color: var(--rich-black);
  animation: glow 2s ease-in-out infinite alternate;
}

.game-image-container {
  position: relative;
  overflow: hidden;
  height: 220px;
}

.game-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.game-card:hover .game-image-container img {
  transform: scale(1.1);
}

.game-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 1.5rem 1rem 1rem;
  transform: translateY(100%);
  transition: transform 0.4s ease;
}

.game-card:hover .game-overlay {
  transform: translateY(0);
}

.game-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--white);
  font-size: 0.9rem;
  font-weight: bold;
}

.game-stats span {
  background: rgba(255, 215, 0, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  border: 1px solid var(--primary-gold);
}

.game-content {
  padding: 2rem;
}

.game-content h3 {
  font-size: 1.5rem;
  color: var(--primary-gold);
  margin-bottom: 1rem;
  font-family: var(--font-primary);
}

.game-content p {
  color: var(--cream);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.game-content strong {
  color: var(--primary-gold);
  font-weight: bold;
}

.game-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.feature-tag {
  background: rgba(255, 215, 0, 0.1);
  color: var(--primary-gold);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  border: 1px solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
}

.feature-tag:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.05);
}

/* Game Buttons */
.game-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 56px;
  will-change: transform;
}

.game-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.game-btn:hover::before {
  left: 100%;
}

.game-btn.primary {
  background: var(--gold-gradient);
  color: var(--rich-black);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.game-btn.primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6);
}

.game-btn.secondary {
  background: var(--green-gradient);
  color: var(--white);
  box-shadow: 0 6px 20px rgba(80, 200, 120, 0.4);
}

.game-btn.secondary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 30px rgba(80, 200, 120, 0.6);
}

.game-btn.tertiary {
  background: var(--red-gradient);
  color: var(--white);
  box-shadow: 0 6px 20px rgba(220, 20, 60, 0.4);
}

.game-btn.tertiary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 30px rgba(220, 20, 60, 0.6);
}

.game-btn.quaternary {
  background: linear-gradient(135deg, #4A4A4A, #2D2D2D);
  color: var(--primary-gold);
  border: 2px solid var(--primary-gold);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2);
}

.game-btn.quaternary:hover {
  background: var(--gold-gradient);
  color: var(--rich-black);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6);
}

.btn-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.game-btn:hover .btn-arrow {
  transform: translateX(5px);
}

/* Games Footer */
.games-footer {
  text-align: center;
  padding-top: 3rem;
  border-top: 2px solid rgba(255, 215, 0, 0.2);
}

.games-note {
  color: var(--silver);
  font-size: 1rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.note-icon {
  font-size: 1.2rem;
}

.view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  color: var(--primary-gold);
  border: 2px solid var(--primary-gold);
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
}

.view-all-btn:hover {
  background: var(--gold-gradient);
  color: var(--rich-black);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
}

.view-all-btn:hover .btn-arrow {
  transform: translateX(5px);
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes glow {
  0% { box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4); }
  100% { box-shadow: 0 4px 25px rgba(255, 215, 0, 0.8); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Bonus Section */
.bonus {
  background: var(--red-gradient);
  border-radius: 25px;
  padding: 3rem;
  margin-bottom: 4rem;
  text-align: center;
  border: 3px solid var(--primary-gold);
  box-shadow: var(--red-shadow);
  position: relative;
  overflow: hidden;
}

.bonus::before {
  content: '💰';
  position: absolute;
  font-size: 8rem;
  opacity: 0.1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.bonus ul {
  list-style: none;
  margin: 2rem 0;
  position: relative;
  z-index: 1;
}

.bonus li {
  font-size: 1.3rem;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.bonus li:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: translateX(10px);
}

/* Testimonials Section */
.testimonials {
  background: rgba(0, 100, 0, 0.1);
  border-radius: 20px;
  padding: 3rem;
  border: 2px solid var(--primary-gold);
  margin-bottom: 4rem;
}

.testimonial-box {
  background: var(--dark-gradient);
  padding: 2rem;
  margin: 2rem 0;
  border-radius: 15px;
  color: var(--cream);
  box-shadow: var(--dark-shadow);
  position: relative;
  transition: all 0.3s ease;
  will-change: transform;
  contain: layout style;
}

.testimonial-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}

.testimonial-text {
  font-style: italic;
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.testimonial-author {
  font-size: 1rem;
  color: var(--primary-gold);
  text-align: right;
  margin: 0;
  font-weight: bold;
}

/* CTA Form Section */
.cta-form {
  background: var(--gold-gradient);
  color: var(--rich-black);
  border-radius: 25px;
  padding: 3rem;
  text-align: center;
  box-shadow: var(--gold-shadow);
  border: 3px solid var(--casino-red);
  margin-bottom: 4rem;
}

.cta-form h2 {
  color: var(--rich-black);
  text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.3);
}

.cta-form form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 400px;
  margin: 2rem auto 0;
}

.cta-form input {
  padding: 1rem;
  border: 3px solid var(--casino-red);
  border-radius: 10px;
  font-size: 1.1rem;
  background: var(--white);
  color: var(--rich-black);
  transition: all 0.3s ease;
}

.cta-form input:focus {
  outline: none;
  border-color: var(--deep-red);
  box-shadow: 0 0 15px rgba(220, 20, 60, 0.3);
  transform: scale(1.02);
}

.cta-form input::placeholder {
  color: #666;
  font-style: italic;
}

.cta-form button {
  background: var(--red-gradient);
  color: var(--white);
  border: 3px solid var(--rich-black);
  font-size: 1.3rem;
  padding: 1.2rem;
  margin-top: 1rem;
}

.cta-form button:hover {
  background: var(--deep-red);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(139, 0, 0, 0.5);
}

/* FAQ Section */
.faq {
  background: rgba(13, 13, 13, 0.8);
  border-radius: 20px;
  padding: 3rem;
  border: 2px solid var(--silver);
}

.faq details {
  background: var(--dark-gradient);
  margin: 1.5rem 0;
  border-radius: 10px;
  border: 2px solid var(--primary-gold);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq details:hover {
  border-color: var(--casino-red);
  box-shadow: 0 5px 15px rgba(220, 20, 60, 0.2);
}

.faq summary {
  padding: 1.5rem;
  background: var(--red-gradient);
  color: var(--white);
  cursor: pointer;
  font-weight: bold;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
}

.faq summary:hover {
  background: var(--deep-red);
  padding-left: 2rem;
}

.faq summary::marker {
  color: var(--primary-gold);
  font-size: 1.5rem;
}

.faq details[open] summary {
  background: var(--casino-red);
  border-bottom: 2px solid var(--primary-gold);
}

.faq details p {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  color: var(--cream);
  line-height: 1.8;
}

/* Footer */
footer {
  background: var(--rich-black);
  padding: 3rem 2rem;
  text-align: center;
  border-top: 3px solid var(--primary-gold);
  margin-top: 4rem;
  contain: layout style;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

footer .age-warning {
  margin: 1rem 0;
  color: var(--casino-red);
  font-weight: bold;
  font-size: 1.1rem;
}

footer .copyright {
  margin: 1rem 0;
  color: var(--silver);
}

footer nav {
  margin-top: 2rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

footer nav a {
  color: var(--primary-gold);
  text-decoration: none;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  font-weight: bold;
  border-radius: 4px;
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

footer nav a:hover,
footer nav a:focus {
  color: var(--casino-red);
  text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
  background: rgba(255, 215, 0, 0.1);
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

footer nav .separator {
  color: var(--silver);
  margin: 0 0.5rem;
}

/* Back to Top Button */
#back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-gold), #b8860b);
  color: var(--rich-black);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  will-change: transform, opacity;
  contain: layout style;
}

#back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

#back-to-top:hover,
#back-to-top:focus {
  background: linear-gradient(135deg, #ffd700, var(--primary-gold));
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

#back-to-top:active {
  transform: translateY(0);
  box-shadow: 0 2px 15px rgba(255, 215, 0, 0.3);
}

.back-to-top-icon {
  font-size: 1.2rem;
  line-height: 1;
  margin-bottom: 2px;
}

.back-to-top-text {
  font-size: 0.7rem;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Article Section */
.article {
  background: var(--dark-gradient);
  padding: 4rem 2rem;
  margin: 4rem auto;
  max-width: 1200px;
  border-radius: 20px;
  border: 2px solid rgba(255, 215, 0, 0.2);
  box-shadow: var(--dark-shadow);
  position: relative;
  overflow: hidden;
  contain: layout style;
}

.article::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-gold), var(--casino-red), var(--primary-gold));
  animation: shimmer 3s ease-in-out infinite;
}

.article article {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.3);
  padding: 3rem;
  border-radius: 15px;
  position: relative;
  text-align: center;
}

.article p {
  color: var(--cream);
  font-size: 1.1rem;
  line-height: 1.8;
  margin: 0;
  text-align: justify;
  text-indent: 2rem;
}

.article p::first-line {
  font-weight: 600;
  color: var(--primary-gold);
}

.article h2 {
  color: var(--primary-gold);
  font-size: 2rem;
  margin: 0 auto 2rem auto;
  max-width: 1200px;
  text-align: center;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.article h3 {
  color: var(--casino-red);
  font-size: 1.5rem;
  margin: 2rem 0 1rem 0;
  border-bottom: 2px solid rgba(220, 20, 60, 0.3);
  padding-bottom: 0.5rem;
}

/* Loading Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* Scroll Animations */
.hero, .usp, .games, .bonus, .testimonials, .cta-form, .faq {
  animation: slideInUp 0.8s ease-out;
}

.feature, .games article, .testimonial-box {
  animation: fadeInScale 0.6s ease-out;
}

/* Casino-specific Visual Effects */
.casino-chip {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gold-gradient);
  border: 4px solid var(--casino-red);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--rich-black);
  box-shadow: var(--gold-shadow);
  margin: 0 10px;
  animation: pulse 2s infinite;
}

.card-effect {
  background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
  border-radius: 10px;
  box-shadow:
    inset 5px 5px 10px #bebebe,
    inset -5px -5px 10px #ffffff;
}

/* Slot Machine Effect */
.slot-reel {
  background: var(--dark-gradient);
  border: 3px solid var(--primary-gold);
  border-radius: 10px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.slot-reel::before {
  content: '🎰 🍒 🍋 ⭐ 💎';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 500%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  font-size: 2rem;
  animation: slot-spin 3s linear infinite;
}

@keyframes slot-spin {
  0% { transform: translateY(0); }
  100% { transform: translateY(-80%); }
}

/* Enhanced Responsive Design with Multiple Breakpoints */

/* Intermediate breakpoint for small tablets and large phones */
@media (max-width: 768px) and (min-width: 481px) {
  .header-container {
    padding: 0.5rem 1.5rem;
  }

  .logo-img {
    height: 45px;
  }

  .nav-link {
    font-size: 1rem;
    padding: 0.7rem 1.1rem;
  }
}

/* Main mobile breakpoint */
@media (max-width: 768px) {
  :root {
    --font-size-base: 14px;
  }

  /* Enhanced Mobile Header */
  .header-container {
    padding: 0.5rem 1rem;
    height: 70px; /* Slightly reduced for mobile */
  }

  .main-nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--dark-gradient);
    border-top: 2px solid var(--primary-gold);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.9);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
    contain: layout style paint;
    z-index: 999; /* Ensure menu appears above other content */
    max-height: calc(100vh - 70px); /* Prevent menu from going off-screen */
    overflow-y: auto; /* Allow scrolling if menu is too tall */
  }

  .main-nav.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  /* Prevent body scroll when mobile menu is open */
  body.mobile-menu-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
  }

  /* Enhanced mobile nav links with proper touch targets */
  .nav-link {
    display: flex;
    align-items: center;
    padding: 1.25rem 2rem; /* Increased for better touch targets */
    min-height: 56px; /* Larger touch target for mobile */
    border-radius: 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    font-size: 1.1rem;
    justify-content: flex-start;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  /* Mobile nav link hover effect */
  .nav-link::before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--gold-gradient);
    transition: left 0.3s ease;
    z-index: -1;
  }

  .nav-link:last-child {
    border-bottom: none;
  }

  .nav-link:hover,
  .nav-link:focus {
    color: var(--rich-black);
    transform: translateX(10px);
  }

  .nav-link:hover::before,
  .nav-link:focus::before {
    left: 0;
  }

  /* Enhanced mobile focus states */
  .nav-link:focus {
    outline: 3px solid var(--primary-gold);
    outline-offset: -3px;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .logo-img {
    height: 40px;
  }

  /* Mobile menu overlay for better UX */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 998;
  }

  .mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  /* Hero Carousel Mobile Styles */
  .hero-carousel { opacity: 0.15; }
  .banner-img { max-width: 250px; }
  .carousel-arrow { width: 40px; height: 40px; font-size: 1.2rem; }
  .carousel-nav { padding: 0 1rem; }
  .carousel-dots { bottom: 1rem; }
  .carousel-dot { width: 10px; height: 10px; }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .header-container {
    padding: 0.5rem 0.75rem;
    height: 65px;
  }

  .logo-img {
    height: 35px;
    max-width: 140px;
  }

  .nav-link {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    min-height: 48px; /* Ensure adequate touch target */
  }

  .mobile-menu-toggle {
    padding: 0.6rem;
    min-height: 48px;
    min-width: 48px;
  }

  .hamburger-line {
    width: 24px;
  }

  .main-nav {
    max-height: calc(100vh - 65px); /* Adjust for smaller header */
  }
}

/* Very small screens */
@media (max-width: 320px) {
  .header-container {
    padding: 0.5rem;
    height: 60px;
  }

  .logo-img {
    height: 30px;
    max-width: 120px;
  }

  .nav-link {
    padding: 1rem;
    font-size: 0.95rem;
    min-height: 44px; /* Minimum accessibility standard */
  }

  .mobile-menu-toggle {
    padding: 0.5rem;
    min-height: 44px;
    min-width: 44px;
  }

  .hamburger-line {
    width: 22px;
    height: 2px;
  }

  .main-nav {
    max-height: calc(100vh - 60px); /* Adjust for smallest header */
  }

  /* Hero Carousel Very Small Mobile */
  .hero-carousel { opacity: 0.1; }
  .banner-img { max-width: 180px; }
  .carousel-arrow { width: 35px; height: 35px; font-size: 1rem; }
  .carousel-dots { bottom: 0.5rem; }
  .carousel-dot { width: 8px; height: 8px; }
}

@media (max-width: 320px) {
  .hero {
    min-height: 80vh;
    padding: 1rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  section {
    padding: 2rem 1rem;
  }

  .features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Enhanced mobile games section */
  .games {
    padding: 3rem 1rem;
  }

  .games-header h2 {
    font-size: 2.5rem;
  }

  .games-subtitle {
    font-size: 1.1rem;
  }

  .games-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .game-card {
    border-radius: 20px;
  }

  .game-image-container {
    height: 180px;
  }

  .game-content {
    padding: 1.5rem;
  }

  .game-content h3 {
    font-size: 1.3rem;
  }

  .game-features {
    gap: 0.3rem;
  }

  .feature-tag {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }

  .game-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .games-note {
    font-size: 0.9rem;
    flex-direction: column;
    gap: 0.3rem;
  }

  .feature, .games article {
    padding: 1.5rem;
  }

  button, .cta-button {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .cta-form {
    padding: 2rem 1rem;
  }

  .cta-form form {
    max-width: 100%;
  }

  .bonus, .testimonials, .faq, .article {
    padding: 2rem 1rem;
  }

  .article article {
    padding: 2rem;
  }

  .article p {
    font-size: 1rem;
    text-indent: 1rem;
  }

  .article h2 {
    font-size: 1.8rem;
  }

  .article h3 {
    font-size: 1.3rem;
  }

  .testimonial-box {
    padding: 1.5rem;
  }

  .testimonial-text {
    font-size: 1rem;
  }

  .testimonial-author {
    font-size: 0.9rem;
  }

  footer {
    padding: 2rem 1rem;
  }

  footer nav {
    flex-direction: column;
    gap: 1rem;
  }

  footer nav a {
    display: block;
    margin: 0;
    padding: 0.75rem 1rem;
    width: 100%;
    max-width: 300px;
  }

  footer nav .separator {
    display: none;
  }

  #back-to-top {
    width: 50px;
    height: 50px;
    bottom: 1rem;
    right: 1rem;
    font-size: 0.8rem;
  }

  .back-to-top-icon {
    font-size: 1rem;
  }

  .back-to-top-text {
    font-size: 0.6rem;
  }

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.8rem;
  }

  h3 {
    font-size: 1.4rem;
  }

  .bonus li {
    font-size: 1.1rem;
    padding: 0.8rem;
  }

  /* Footer adjustments for very small screens */
  footer {
    padding: 1.5rem 0.75rem;
  }

  footer .age-warning {
    font-size: 1rem;
  }

  footer nav a {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  #back-to-top {
    width: 45px;
    height: 45px;
    bottom: 0.75rem;
    right: 0.75rem;
  }

  .back-to-top-icon {
    font-size: 0.9rem;
  }

  .back-to-top-text {
    font-size: 0.55rem;
  }

  /* Article adjustments for very small screens */
  .article {
    padding: 1.5rem 0.75rem;
    margin: 2rem 0;
  }

  .article article {
    padding: 1.5rem;
  }

  .article p {
    font-size: 0.95rem;
    text-indent: 0.5rem;
  }

  .article h2 {
    font-size: 1.6rem;
  }

  /* Small mobile games adjustments */
  .games {
    padding: 2rem 0.75rem;
  }

  .games-header {
    margin-bottom: 3rem;
  }

  .games-header h2 {
    font-size: 2rem;
  }

  .games-subtitle {
    font-size: 1rem;
  }

  .game-image-container {
    height: 160px;
  }

  .game-content {
    padding: 1.2rem;
  }

  .game-content h3 {
    font-size: 1.2rem;
  }

  .game-content p {
    font-size: 0.95rem;
  }

  .game-btn {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
    min-height: 48px;
  }

  .view-all-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .cta-form input, .cta-form button {
    font-size: 1rem;
    padding: 0.8rem;
  }
}

/* High-resolution displays */
@media (min-width: 1200px) {
  .features {
    grid-template-columns: repeat(3, 1fr);
  }

  .games {
    grid-template-columns: repeat(2, 1fr);
  }

  section {
    padding: 5rem 2rem;
  }
}

/* Dark mode preference */
@media (prefers-color-scheme: dark) {
  body {
    background: var(--dark-gradient);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced header performance optimizations */
@media (min-width: 769px) {
  .nav-link {
    /* Optimize hover effects for desktop */
    will-change: transform, color, box-shadow;
  }

  .nav-link:hover {
    /* Enhanced desktop hover with subtle glow */
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.4),
      0 0 15px rgba(255, 215, 0, 0.2);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-link {
    border: 2px solid var(--primary-gold);
  }

  .nav-link:focus {
    outline-width: 4px;
  }

  .mobile-menu-toggle:focus {
    outline-width: 4px;
  }
}

/* Print styles */
@media print {
  .main-header {
    position: static;
    box-shadow: none;
    border-bottom: 2px solid #000;
  }

  .mobile-menu-toggle {
    display: none;
  }

  .main-nav {
    position: static;
    background: transparent;
  }

  .nav-list {
    flex-direction: row;
    gap: 1rem;
  }

  .nav-link {
    color: #000;
    padding: 0.25rem 0.5rem;
  }

  body {
    background: white;
    color: black;
    padding-top: 0;
  }

  .hero, .bonus, .cta-form {
    background: white;
    color: black;
  }

  button, .cta-button {
    background: #ccc;
    color: black;
  }

  /* Hero Carousel for very small screens */
  .hero-carousel { opacity: 0.08; }
  .banner-img { max-width: 150px; }
  .carousel-arrow { width: 30px; height: 30px; font-size: 0.9rem; }
  .carousel-nav { padding: 0 0.5rem; }
  .carousel-dots { display: none; } /* Hide dots on very small screens */
}
}
